"""
通义千问大模型实现
基于 langchain_community 中的 ChatTongyi
"""

import os
import logging
from typing import Optional, Dict, List

from langchain_community.chat_models import ChatTongyi
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

logger = logging.getLogger(__name__)


# 环境变量配置
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY")
DASHSCOPE_MODEL = os.getenv("DASHSCOPE_MODEL", "qwen-turbo")
DASHSCOPE_TEMPERATURE = float(os.getenv("DASHSCOPE_TEMPERATURE", "0.7"))


class TongyiLLM:
    """
    通义千问大模型封装类

    提供统一的文本生成接口，支持单轮对话和多轮对话
    基于 langchain_community.chat_models.ChatTongyi 实现
    """

    def __init__(
        self,
        api_key: str = DASHSCOPE_API_KEY,
        model: str = DASHSCOPE_MODEL,
        temperature: float = DASHSCOPE_TEMPERATURE,
        max_tokens: int = 2000,
        top_p: float = 0.8,
        **kwargs,
    ):
        """
        初始化通义千问模型

        Args:
            api_key: DashScope API密钥
            model: 模型名称
            temperature: 温度参数，控制生成的随机性
            max_tokens: 最大生成token数
            top_p: Top-p采样参数
            **kwargs: 其他参数
        """
        self.api_key = api_key or DASHSCOPE_API_KEY
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p

        if not self.api_key:
            raise ValueError(
                "未找到通义千问API密钥，请设置DASHSCOPE_API_KEY环境变量或传入api_key参数"
            )

        # 初始化 ChatTongyi 实例
        self.chat_model = ChatTongyi(
            dashscope_api_key=self.api_key,
            model_name=self.model,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            top_p=self.top_p,
            **kwargs,
        )

        logger.info(f"通义千问模型初始化完成，使用模型: {self.model}")

    def generate(
        self, prompt: str, system_prompt: Optional[str] = None, **kwargs
    ) -> str:
        """
        生成文本响应

        Args:
            prompt: 用户输入的提示词
            system_prompt: 系统提示词（可选）
            **kwargs: 其他生成参数

        Returns:
            str: 生成的文本响应

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            # 构建消息列表
            messages = []

            if system_prompt:
                messages.append(SystemMessage(content=system_prompt))

            messages.append(HumanMessage(content=prompt))

            logger.debug(f"调用通义千问API，消息数: {len(messages)}")

            # 调用 ChatTongyi
            response = self.chat_model.invoke(messages)

            content = response.content
            logger.debug(f"通义千问响应成功: {content[:100]}...")
            return content

        except Exception as e:
            logger.error(f"通义千问生成文本失败: {str(e)}")
            raise

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        多轮对话接口

        Args:
            messages: 对话消息列表，格式为 [{"role": "user/assistant/system", "content": "..."}]
            **kwargs: 其他生成参数

        Returns:
            str: 生成的回复

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            # 转换消息格式为 langchain 格式
            langchain_messages = []
            for msg in messages:
                role = msg["role"]
                content = msg["content"]

                if role == "system":
                    langchain_messages.append(SystemMessage(content=content))
                elif role == "user":
                    langchain_messages.append(HumanMessage(content=content))
                elif role == "assistant":
                    langchain_messages.append(AIMessage(content=content))
                else:
                    logger.warning(f"未知的消息角色: {role}")

            logger.debug(f"调用通义千问多轮对话API，消息数: {len(langchain_messages)}")

            # 调用 ChatTongyi
            response = self.chat_model.invoke(langchain_messages)

            content = response.content
            logger.debug(f"通义千问多轮对话响应成功: {content[:100]}...")
            return content

        except Exception as e:
            logger.error(f"通义千问多轮对话失败: {str(e)}")
            raise

    def translate(
        self,
        text: str,
        source_lang: str,
        target_lang: str,
        context: Optional[str] = None,
    ) -> str:
        """
        翻译文本

        Args:
            text: 待翻译的文本
            source_lang: 源语言
            target_lang: 目标语言
            context: 翻译上下文（可选）

        Returns:
            str: 翻译结果
        """
        # 构建翻译提示词
        system_prompt = f"""你是一个专业的翻译助手，请将以下文本从{source_lang}翻译成{target_lang}。

翻译要求：
1. 保持原文的语义和语调
2. 使用自然、流畅的表达
3. 如果是技术术语，请保持专业性
4. 只返回翻译结果，不要添加额外说明
"""

        if context:
            system_prompt += f"\n\n翻译上下文：{context}"

        user_prompt = f"请翻译以下文本：\n{text}"

        return self.generate(user_prompt, system_prompt)


def create_tongyi_llm(
    api_key: Optional[str] = None,
    model: str = "qwen-turbo",
    temperature: float = 0.7,
    max_tokens: int = 2000,
    top_p: float = 0.8,
    **kwargs,
) -> TongyiLLM:
    """
    创建通义千问LLM实例的便捷函数

    Args:
        api_key: API密钥，如果不提供则从环境变量DASHSCOPE_API_KEY或QWEN_API_KEY获取
        model: 模型名称，默认为qwen-turbo
        temperature: 温度参数，控制生成的随机性
        max_tokens: 最大生成token数
        top_p: Top-p采样参数
        **kwargs: 其他参数

    Returns:
        TongyiLLM: 通义千问模型实例

    Raises:
        ValueError: 当API密钥未提供且环境变量中也没有时抛出
    """
    # 获取API密钥，支持多个环境变量名
    if api_key is None:
        api_key = os.getenv("DASHSCOPE_API_KEY") or os.getenv("QWEN_API_KEY")
        if not api_key:
            raise ValueError(
                "未找到通义千问API密钥，请设置DASHSCOPE_API_KEY或QWEN_API_KEY环境变量或传入api_key参数"
            )

    # 创建并返回实例
    return TongyiLLM(
        api_key=api_key,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        **kwargs,
    )


# 支持的模型列表
SUPPORTED_MODELS = [
    "qwen-turbo",
    "qwen-plus",
    "qwen-max",
    "qwen-max-1201",
    "qwen-max-longcontext",
]


def get_supported_models() -> List[str]:
    """
    获取支持的模型列表

    Returns:
        List[str]: 支持的模型名称列表
    """
    return SUPPORTED_MODELS.copy()
