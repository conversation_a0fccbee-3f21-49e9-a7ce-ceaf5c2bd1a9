"""
通义千问大模型实现
基于阿里云DashScope API
"""

import os
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

import dashscope
from dashscope import Generation

logger = logging.getLogger(__name__)





class TongyiLLM:
    """
    通义千问大模型封装类

    提供统一的文本生成接口，支持单轮对话和多轮对话
    """

    def __init__(
        self,
        api_key: str,
        base_url: str,
        model: str = "qwen-turbo",
        temperature: float = 0.7,
    ):
        """
        初始化通义千问模型

        Args:
            config: 模型配置
        """
        self.config = config
        self._setup_client()

    def _setup_client(self):
        """设置DashScope客户端"""
        dashscope.api_key = self.config.api_key
        logger.info(f"通义千问模型初始化完成，使用模型: {self.config.model}")

    def generate(
        self, prompt: str, system_prompt: Optional[str] = None, **kwargs
    ) -> str:
        """
        生成文本响应

        Args:
            prompt: 用户输入的提示词
            system_prompt: 系统提示词（可选）
            **kwargs: 其他生成参数

        Returns:
            str: 生成的文本响应

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            # 构建消息列表
            messages = []

            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            messages.append({"role": "user", "content": prompt})

            # 合并配置参数
            generation_params = {
                "model": self.config.model,
                "messages": messages,
                "temperature": kwargs.get("temperature", self.config.temperature),
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "top_p": kwargs.get("top_p", self.config.top_p),
                "result_format": "message",
            }

            logger.debug(f"调用通义千问API，参数: {generation_params}")

            # 调用API
            response = Generation.call(**generation_params)

            if response.status_code == 200:
                content = response.output.choices[0].message.content
                logger.debug(f"通义千问响应成功: {content[:100]}...")
                return content
            else:
                error_msg = f"通义千问API调用失败: {response.code} - {response.message}"
                logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            logger.error(f"通义千问生成文本失败: {str(e)}")
            raise

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        多轮对话接口

        Args:
            messages: 对话消息列表，格式为 [{"role": "user/assistant/system", "content": "..."}]
            **kwargs: 其他生成参数

        Returns:
            str: 生成的回复

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            # 合并配置参数
            generation_params = {
                "model": self.config.model,
                "messages": messages,
                "temperature": kwargs.get("temperature", self.config.temperature),
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "top_p": kwargs.get("top_p", self.config.top_p),
                "result_format": "message",
            }

            logger.debug(f"调用通义千问多轮对话API，消息数: {len(messages)}")

            # 调用API
            response = Generation.call(**generation_params)

            if response.status_code == 200:
                content = response.output.choices[0].message.content
                logger.debug(f"通义千问多轮对话响应成功: {content[:100]}...")
                return content
            else:
                error_msg = f"通义千问API调用失败: {response.code} - {response.message}"
                logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            logger.error(f"通义千问多轮对话失败: {str(e)}")
            raise

    def translate(
        self,
        text: str,
        source_lang: str,
        target_lang: str,
        context: Optional[str] = None,
    ) -> str:
        """
        翻译文本

        Args:
            text: 待翻译的文本
            source_lang: 源语言
            target_lang: 目标语言
            context: 翻译上下文（可选）

        Returns:
            str: 翻译结果
        """
        # 构建翻译提示词
        system_prompt = f"""你是一个专业的翻译助手，请将以下文本从{source_lang}翻译成{target_lang}。

翻译要求：
1. 保持原文的语义和语调
2. 使用自然、流畅的表达
3. 如果是技术术语，请保持专业性
4. 只返回翻译结果，不要添加额外说明"""

        if context:
            system_prompt += f"\n\n翻译上下文：{context}"

        user_prompt = f"请翻译以下文本：\n{text}"

        return self.generate(user_prompt, system_prompt)


def create_tongyi_llm(
    api_key: Optional[str] = None,
    model: str = "qwen-turbo",
    temperature: float = 0.7,
    max_tokens: int = 2000,
    top_p: float = 0.8,
    **kwargs,
) -> TongyiLLM:
    """
    创建通义千问LLM实例的便捷函数

    Args:
        api_key: API密钥，如果不提供则从环境变量QWEN_API_KEY获取
        model: 模型名称，默认为qwen-turbo
        temperature: 温度参数，控制生成的随机性
        max_tokens: 最大生成token数
        top_p: Top-p采样参数
        **kwargs: 其他参数

    Returns:
        TongyiLLM: 通义千问模型实例

    Raises:
        ValueError: 当API密钥未提供且环境变量中也没有时抛出
    """
    # 获取API密钥
    if api_key is None:
        api_key = os.getenv("QWEN_API_KEY")
        if not api_key:
            raise ValueError(
                "未找到通义千问API密钥，请设置QWEN_API_KEY环境变量或传入api_key参数"
            )

    # 创建配置
    config = TongyiConfig(
        api_key=api_key,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
    )

    # 创建并返回实例
    return TongyiLLM(config)


# 支持的模型列表
SUPPORTED_MODELS = [
    "qwen-turbo",
    "qwen-plus",
    "qwen-max",
    "qwen-max-1201",
    "qwen-max-longcontext",
]


def get_supported_models() -> List[str]:
    """
    获取支持的模型列表

    Returns:
        List[str]: 支持的模型名称列表
    """
    return SUPPORTED_MODELS.copy()
