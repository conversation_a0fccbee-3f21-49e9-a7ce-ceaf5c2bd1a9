# 通义千问LLM模块

这个模块提供了通义千问大模型的Python封装，基于阿里云DashScope API实现。

## 功能特性

- 🤖 **统一接口**: 提供简洁易用的API接口
- 💬 **多轮对话**: 支持上下文感知的多轮对话
- 🌍 **翻译功能**: 内置专业的翻译功能
- ⚙️ **灵活配置**: 支持自定义模型参数
- 📝 **完整日志**: 详细的调用日志记录

## 快速开始

### 1. 环境配置

首先确保已安装依赖：
```bash
pip install dashscope
```

然后设置环境变量：
```bash
export QWEN_API_KEY="your-api-key-here"
```

或在`.env`文件中配置：
```
QWEN_API_KEY=your-api-key-here
```

### 2. 基本使用

#### 使用便捷函数创建实例

```python
from app.llms import get_llm, LLMType

# 使用默认配置创建实例
llm = get_llm(LLMType.TONGYI)

# 简单文本生成
response = llm.generate("请介绍一下Python编程语言")
print(response)
```

#### 直接创建实例

```python
from app.llms.tongyi_llm import create_tongyi_llm

# 使用默认配置
llm = create_tongyi_llm()

# 使用自定义配置
llm = create_tongyi_llm(
    model="qwen-plus",
    temperature=0.7,
    max_tokens=1000
)
```

### 3. 高级功能

#### 系统提示词

```python
system_prompt = "你是一个专业的Python编程助手"
user_prompt = "什么是装饰器？"

response = llm.generate(user_prompt, system_prompt)
print(response)
```

#### 多轮对话

```python
messages = [
    {"role": "system", "content": "你是一个友好的助手"},
    {"role": "user", "content": "你好"},
    {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"},
    {"role": "user", "content": "请介绍一下机器学习"}
]

response = llm.chat(messages)
print(response)
```

#### 翻译功能

```python
# 中文翻译成英文
translation = llm.translate(
    text="你好，世界！",
    source_lang="中文",
    target_lang="英文"
)
print(translation)  # 输出: Hello, world!

# 带上下文的翻译
translation = llm.translate(
    text="这个bug很难修复",
    source_lang="中文", 
    target_lang="英文",
    context="这是一个关于软件开发的对话"
)
print(translation)
```

## API参考

### TongyiLLM类

#### 构造函数
```python
TongyiLLM(config: TongyiConfig)
```

#### 主要方法

##### generate()
```python
def generate(
    self, 
    prompt: str, 
    system_prompt: Optional[str] = None,
    **kwargs
) -> str
```
生成文本响应。

**参数:**
- `prompt`: 用户输入的提示词
- `system_prompt`: 系统提示词（可选）
- `**kwargs`: 其他生成参数（temperature, max_tokens, top_p等）

**返回:** 生成的文本响应

##### chat()
```python
def chat(
    self, 
    messages: List[Dict[str, str]], 
    **kwargs
) -> str
```
多轮对话接口。

**参数:**
- `messages`: 对话消息列表
- `**kwargs`: 其他生成参数

**返回:** 生成的回复

##### translate()
```python
def translate(
    self, 
    text: str, 
    source_lang: str, 
    target_lang: str,
    context: Optional[str] = None
) -> str
```
翻译文本。

**参数:**
- `text`: 待翻译的文本
- `source_lang`: 源语言
- `target_lang`: 目标语言
- `context`: 翻译上下文（可选）

**返回:** 翻译结果

### 便捷函数

#### create_tongyi_llm()
```python
def create_tongyi_llm(
    api_key: Optional[str] = None,
    model: str = "qwen-turbo",
    temperature: float = 0.7,
    max_tokens: int = 2000,
    top_p: float = 0.8,
    **kwargs
) -> TongyiLLM
```

#### get_supported_models()
```python
def get_supported_models() -> List[str]
```
获取支持的模型列表。

## 支持的模型

- `qwen-turbo`: 快速响应，适合日常对话
- `qwen-plus`: 平衡性能和质量
- `qwen-max`: 最高质量，适合复杂任务
- `qwen-max-1201`: 最新版本的qwen-max
- `qwen-max-longcontext`: 支持长上下文

## 配置参数

### TongyiConfig

- `api_key`: API密钥
- `model`: 模型名称（默认: "qwen-turbo"）
- `temperature`: 温度参数，控制随机性（默认: 0.7）
- `max_tokens`: 最大生成token数（默认: 2000）
- `top_p`: Top-p采样参数（默认: 0.8）

## 错误处理

模块会抛出以下异常：

- `ValueError`: 当API密钥未提供时
- `Exception`: 当API调用失败时

建议使用try-catch块处理异常：

```python
try:
    response = llm.generate("你好")
    print(response)
except ValueError as e:
    print(f"配置错误: {e}")
except Exception as e:
    print(f"API调用失败: {e}")
```

## 日志记录

模块使用Python标准logging模块记录日志。可以通过以下方式配置日志级别：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 测试

运行测试脚本验证功能：

```bash
cd backend
python test_tongyi_llm.py
```

## 注意事项

1. 确保API密钥有效且有足够的配额
2. 注意API调用频率限制
3. 大模型响应时间可能较长，建议设置合适的超时时间
4. 翻译功能基于提示词工程，质量可能因语言对而异
